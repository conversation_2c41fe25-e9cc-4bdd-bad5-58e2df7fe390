<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <meta name="theme-color" content="#4361ee">
    <title>数字人语音交互</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="ws-bridge.js"></script>
    <style>
        :root {
            --primary-color: #4361ee;
            --primary-hover: #3a56d4;
            --secondary-color: #f72585;
            --text-primary: #333;
            --text-secondary: #666;
            --background-light: #ffffff;
            --background-dark: #f8f9fa;
            --border-color: #e0e0e0;
            --success-color: #2ec4b6;
            --warning-color: #ff9f1c;
            --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            --border-radius: 12px;
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            margin: 0;
            padding: 10px;
            background: transparent;
            color: var(--text-primary);
            font-size: 14px;
            /*line-height: 1.5;*/
        }

        .asr-container {
            width: 100%;
            border-radius: var(--border-radius);
            max-width: 500px;
            margin: 0 auto;
            /*overflow: hidden;*/
        }

        .control-section {
            margin-bottom: 16px;
        }

        .hidden {
            display: none;
        }

        /* 输入框样式 */
        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-sizing: border-box;
            font-size: 14px;
            transition: var(--transition);
        }

        input[type="text"]:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        }

        /* 结果显示区域样式 */
        #varArea, #text-input-area {
            width: 95%;
            padding: 12px;
            margin-bottom: 16px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-family: inherit;
            font-size: 16px;
            background-color: var(--background-light);
            transition: var(--transition);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            box-sizing: border-box;
            height: 60px;
            resize: none;
        }

        #varArea:focus, #text-input-area:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        }

        /* 状态提示 */
        #info_div {
            margin: 15px 0;
            font-weight: 500;
            color: var(--text-primary);
            text-align: center;
            padding: 10px;
            border-radius: 20px;
            background-color: rgba(67, 97, 238, 0.1);
        }

        /* 按钮区域 */
        .button-group {
            display: flex;
            justify-content: space-between;
            gap: 10px;
            margin-bottom: 16px;
        }

        button {
            padding: 10px 16px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: var(--transition);
            min-width: 80px;
        }

        button:hover {
            background-color: var(--primary-hover);
            transform: translateY(-1px);
        }

        button:active {
            transform: translateY(0);
        }

        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
            transform: none;
        }

        #btnStart {
            background-color: var(--success-color);
        }

        #btnStart:hover {
            background-color: #25b0a3;
        }

        #btnStop {
            background-color: var(--warning-color);
        }

        #btnStop:hover {
            background-color: #f59000;
        }

        /* 音频播放器 */
        audio {
            width: 100%;
            height: 40px;
            border-radius: 8px;
            margin-top: 8px;
            display: none; /* 隐藏音频播放器 */
        }

        /* 单选按钮组样式 */
        .radio-group {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 16px;
        }

        .radio-option {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .radio-option input[type="radio"] {
            appearance: none;
            -webkit-appearance: none;
            width: 18px;
            height: 18px;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            margin-right: 8px;
            display: grid;
            place-content: center;
            transition: var(--transition);
        }

        .radio-option input[type="radio"]::before {
            content: "";
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: var(--primary-color);
            transform: scale(0);
            transition: var(--transition);
        }

        .radio-option input[type="radio"]:checked {
            border-color: var(--primary-color);
        }

        .radio-option input[type="radio"]:checked::before {
            transform: scale(1);
        }

        .radio-option label {
            font-size: 14px;
            color: var(--text-primary);
            cursor: pointer;
        }

        /* 文件上传样式 */
        .file-upload {
            position: relative;
            overflow: hidden;
            display: inline-block;
            width: 100%;
        }

        .file-upload-label {
            display: block;
            padding: 10px 16px;
            background-color: var(--background-dark);
            color: var(--text-primary);
            border: 1px dashed var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            text-align: center;
            transition: var(--transition);
        }

        .file-upload-label:hover {
            background-color: #f0f0f0;
        }

        .file-upload input[type="file"] {
            font-size: 100px;
            opacity: 0;
            position: absolute;
            right: 0;
            top: 0;
            cursor: pointer;
        }

        /* 热词输入框 */
        #varHot {
            width: 100%;
            height: 80px;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            resize: vertical;
            font-family: inherit;
            font-size: 14px;
            transition: var(--transition);
        }

        #varHot:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        }

        /* 分割线 */
        .divider {
            height: 1px;
            background-color: var(--border-color);
            margin: 16px 0;
        }

        /* 标题样式 */
        h4 {
            margin: 0 0 12px 0;
            color: var(--text-primary);
            font-weight: 600;
            font-size: 15px;
        }

        /* 开关样式 */
        .switch-container {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }

        .switch-container label {
            margin: 0 10px 0 0;
            cursor: pointer;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:focus + .slider {
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        }

        input:checked + .slider:before {
            transform: translateX(20px);
        }

        /* 按住说话按钮容器 */
        .press-hold-container {
            display: flex;
            justify-content: center;
            margin: 15px 0;
        }

        /* 按住说话按钮样式 */
        .press-hold-button {
            width: 100px;
            height: 100px;
            padding: 0;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            /* display: flex; */
            align-items: center;
            justify-content: center;
        }

        .press-hold-button svg {
            width: 40px;
            height: 40px;
        }

        .press-hold-button:hover {
            background-color: var(--primary-hover);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }

        .press-hold-button.recording {
            background-color: var(--secondary-color);
            transform: scale(0.95);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        /* 用户连接区域样式 */
        .container {
            border: 1px solid #ddd;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .input-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 10px;
            box-sizing: border-box;
            font-size: 16px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
        }

        select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        }

        select option {
            padding: 10px;
            font-size: 16px;
        }

        .status {
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
        }

        .status-connected {
            color: var(--success-color);
        }

        .status-disconnected {
            color: var(--secondary-color);
        }

        /* 客户机选择区域 */
        .current-client-info {
            background-color: rgba(46, 196, 182, 0.1);
            padding: 12px;
            border-radius: 10px;
            text-align: center;
            margin-top: 10px;
            border: 1px solid rgba(46, 196, 182, 0.2);
        }

        /* 自定义客户端滚动列表 */
        .client-list-container {
            width: 100%;
            max-height: 180px;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            background-color: white;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 15px;
        }

        .client-list {
            width: 100%;
            max-height: 180px;
            overflow-y: auto;
            padding: 0;
            -webkit-overflow-scrolling: touch; /* 提升iOS滚动体验 */
        }

        .client-item {
            padding: 12px 15px;
            font-size: 15px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            display: flex;
            align-items: center;
            -webkit-tap-highlight-color: rgba(46, 196, 182, 0.1); /* 优化移动端点击效果 */
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(5px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .client-item:active {
            background-color: rgba(46, 196, 182, 0.15); /* 点击时的反馈效果 */
        }

        .client-item:last-child {
            border-bottom: none;
        }

        .client-item:hover {
            background-color: #f8f9fa;
        }

        .client-item.selected {
            background-color: rgba(46, 196, 182, 0.1);
            font-weight: 500;
        }

        .client-item.selected::after {
            content: "✓";
            position: absolute;
            right: 15px;
            color: var(--success-color);
            font-weight: bold;
        }

        .client-item.empty-item {
            color: #999;
            cursor: default;
            text-align: center;
            justify-content: center;
            padding: 20px 15px;
        }

        /* 消息容器样式 */
        .message-container {
            height: 150px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 10px;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .message {
            padding: 8px 12px;
            margin-bottom: 8px;
            border-radius: 8px;
            font-size: 14px;
            line-height: 1.4;
            word-break: break-word;
            position: relative;
        }

        .message-sender {
            font-weight: 500;
            margin-bottom: 4px;
        }

        .message-content {
            margin-bottom: 2px;
        }

        .message-time {
            font-size: 11px;
            color: #999;
            text-align: right;
        }

        .sent {
            background-color: #e3f2fd;
            text-align: right;
            margin-left: 15%;
            border-bottom-right-radius: 2px;
        }

        .received {
            background-color: #f5f5f5;
            margin-right: 15%;
            border-bottom-left-radius: 2px;
        }

        .system {
            background-color: #fff9c4;
            font-style: italic;
            border-radius: 4px;
            text-align: center;
            margin: 5px 10%;
            font-size: 13px;
            color: #6d4c41;
        }

        /* 媒体查询优化手机端显示 */
        @media (max-width: 480px) {
            body {
                padding: 8px;
            }

            .container {
                padding: 12px;
                margin-bottom: 15px;
            }

            h4 {
                font-size: 16px;
                margin-bottom: 10px;
            }

            .button-group {
                flex-direction: row;
            }
            
            /* 媒体上传按钮在移动端的样式 */
            #upload-media-btn {
                padding: 6px 5px;
                font-size: 11px;
            }
            
            #upload-media-btn svg {
                margin-right: 2px;
            }
            
            /* 媒体预览在移动端的样式 */
            .media-preview-container img,
            .media-preview-container video {
                max-height: 130px;
            }
            
            /* 媒体上传状态在移动端的样式 */
            #media-upload-status {
                padding: 10px;
                font-size: 13px;
            }
            
            /* 发送和取消按钮在移动端的样式 */
            #send-media-btn, #cancel-media-btn {
                padding: 5px 8px;
                font-size: 11px;
            }

            /* button {
                min-width: 0;
                flex: 1;
                padding: 10px 5px;
                font-size: 13px;
            } */
        }

        /* 消息类型按钮样式 */
        .message-type-btn {
            padding: 6px 3px;
            background-color: var(--background-light);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: var(--transition);
            min-width: 45px;
        }

        .message-type-btn:hover {
            background-color: rgba(67, 97, 238, 0.05);
        }

        .message-type-btn.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        /* 文本输入区域容器 */
        .text-input-container {
            position: relative;
            margin-bottom: 15px;
        }

        .text-input-buttons {
            position: absolute;
            right: 10px;
            bottom: 10px;
            display: flex;
            gap: 5px;
            align-items: center;
        }

        /* 媒体上传相关样式 */
        #media-upload-status {
            border-radius: 10px;
            padding: 12px;
            margin-bottom: 15px;
            background-color: rgba(67, 97, 238, 0.05);
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .media-preview-container {
            margin-top: 10px;
            text-align: center;
            display: none;
        }

        .media-preview-container img {
            max-width: 100%;
            max-height: 150px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .media-upload-buttons {
            position: relative;
        }

        .media-upload-progress {
            position: absolute;
            bottom: -5px;
            left: 0;
            height: 3px;
            background-color: var(--primary-color);
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
<div class="asr-container">
    <!-- 隐藏的配置项 -->
    <input id="wssip" type="text" onchange="addresschange()" value="wss://www.funasr.com:10096/" class="hidden">
    <a id="wsslink" href="#" class="hidden" onclick="window.open('https://*************:10095/', '_blank')">
        <div id="info_wslink">点击此处手动授权 wss://*************:10095/</div>
    </a>

    <!-- 用户连接区域 - 删除选择客户机部分，只保留状态显示 -->
    <div class="container" id="connection-section" style="padding: 15px; width: 100%; max-width: 100%; box-sizing: border-box;">
        <h4 style="text-align: center; margin-bottom: 15px; font-size: 18px; color: var(--primary-color);">
            数字人远程语音控制
            <span id="connection-indicator" style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background-color: var(--secondary-color); margin-left: 5px; vertical-align: middle;"></span>
        </h4>

        <!-- 客户机状态显示 -->
        <div class="current-client-info" style="text-align: center; padding: 12px; border-radius: 20px; margin-top: 10px;">
            <div style="font-weight: 600; font-size: 14px;">数字人: <span id="current-client" style="color: var(--primary-color); font-weight: bold;">无</span></div>
    </div>

        <!-- 文本输入和发送区域 -->
        <div style="margin-top: 15px; margin-bottom: 15px;">
            <!-- 媒体上传结果提示 -->
            <div id="media-upload-status" style="display: none; margin-bottom: 10px; padding: 8px 12px; border-radius: 8px; background-color: rgba(67, 97, 238, 0.1); font-size: 14px; text-align: center;"></div>
            
            <!-- 文本输入区域容器 -->
            <div class="text-input-container" style="position: relative;">
                <textarea id="text-input" placeholder="请输入要发送的文本..." style="width: 100%; height: 120px; padding: 12px; padding-right: 140px; border: 1px solid var(--border-color); border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.05); resize: none; box-sizing: border-box; font-size: 14px;"></textarea>
                <div style="position: absolute; right: 10px; bottom: 10px; display: flex; gap: 5px; align-items: center;">
                    <!-- 媒体上传按钮 -->
                    <div class="media-upload-buttons" style="display: flex; gap: 5px;">
                        <input type="file" id="media-upload" style="display: none;" accept="image/*,video/*,audio/*">
                        <button id="upload-media-btn" style="padding: 6px 8px; background-color: var(--background-light); color: var(--text-secondary); border: 1px solid var(--border-color); border-radius: 20px; cursor: pointer; font-size: 12px; display: flex; align-items: center;">
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 4px;">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="17 8 12 3 7 8"></polyline>
                                <line x1="12" y1="3" x2="12" y2="15"></line>
                            </svg>
                            媒体
                        </button>
    </div>

                    <!-- 消息类型切换按钮 -->
                    <div style="display: flex; border-radius: 20px; overflow: hidden; border: 1px solid var(--border-color); height: 26px;">
                        <button id="echo-btn" class="message-type-btn active" style="margin: 0; padding: 3px 7px; border-radius: 20px 0 0 20px; border: none; font-size: 12px;">复述</button>
                        <button id="chat-btn" class="message-type-btn" style="margin: 0; padding: 3px 7px; border-radius: 0 20px 20px 0; border: none; font-size: 12px;">对话</button>
                </div>
                    <button id="send-text-btn" style="padding: 6px 5px; background-color: var(--primary-color); color: white; border: none; border-radius: 20px; cursor: pointer; font-size: 12px; font-weight: 500; min-width: 55px;">发送</button>
            </div>
        </div>
    </div>

        <div class="divider"></div>

        <!-- 结果区域 -->
        <div style="margin-bottom: 15px;">
            <div class="control-section" style="margin-bottom: 15px;">
            <div style="display: flex; flex-direction: column; gap: 15px;">
                <div>
                        <h4 style="text-align: center; margin-bottom: 10px; font-size: 15px; opacity: 0.2;">语音识别结果</h4>
                        <textarea id="varArea" readonly placeholder="" style="width: 100%; height: 60px; padding: 12px; background-color: #f9f9f9; color: rgba(0,0,0,0.5); border: none; border-radius: 8px; resize: none; box-sizing: border-box; font-size: 14px; box-shadow: none;"></textarea>
                </div>

                    <div style="display: none;">
                    <h4 style="text-align: center; margin-bottom: 10px; font-size: 15px;">消息记录</h4>
                    <div id="message-container" class="message-container"></div>
                </div>
            </div>
        </div>
    </div>

        <!-- 按住说话按钮 -->
        <div class="press-hold-container" style="margin: 15px 0;">
        <button id="btnPressAndHold" class="press-hold-button">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"></path>
                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                <line x1="12" y1="19" x2="12" y2="23"></line>
                <line x1="8" y1="23" x2="16" y2="23"></line>
            </svg>
        </button>
        </div>
    </div>

    <div class="recoder_mode_div hidden">
        <div class="radio-group">
            <div class="radio-option">
                <input name="recoder_mode" onclick="on_recoder_mode_change()" type="radio" value="mic" checked id="mic-radio">
                <label for="mic-radio">麦克风</label>
            </div>
            <div class="radio-option">
                <input name="recoder_mode" onclick="on_recoder_mode_change()" type="radio" value="file" id="file-radio">
                <label for="file-radio">文件</label>
            </div>
        </div>
    </div>

    <!-- 状态提示 -->
    <div id="info_div" style="margin: 15px 0; text-align: center; font-size: 14px; padding: 10px; border-radius: 20px; background-color: rgba(67, 97, 238, 0.05); width: 100%; box-sizing: border-box;">正在连接到数字人...</div>

    <!-- 按钮区域 -->
    <div class="button-group" style="display: none;">
        <button id="btnConnect" style="display: none;">连接</button>
        <button id="btnStart" style="display: none;">开始</button>
        <button id="btnStop" style="display: none;" disabled>停止</button>
    </div>

    <!-- 音频播放器 -->
    <audio id="audio_record" type="audio/wav" controls></audio>

    <!-- 音量相关控件 -->
    <div class="volume-controls" style="margin-top: 15px;">
        <!-- 音量阈值控制 - 改为开关 -->
        <div id="volume-threshold-control" style="margin-top: 15px; display: none;">
            <div class="switch-container" style="justify-content: space-between;">
                <label for="speaker-detection-toggle" style="margin: 0; font-size: 14px; color: var(--text-secondary);">扬声器检测 (自动暂停/恢复识别)</label>
                <label class="switch">
                    <input type="checkbox" id="speaker-detection-toggle" checked>
                    <span class="slider"></span>
                </label>
            </div>
            <!-- 隐藏的阈值显示，用于调试和保持兼容性 -->
            <span id="threshold-value-display" style="display:none;">1</span>
            <input type="range" id="threshold-slider" min="1" max="100" step="99" value="1" style="display:none;">
        </div>

        <!-- 清空历史记录选项 -->
        <div id="clear-history-control" style="margin-top: 15px; display: none;">
            <div class="switch-container" style="justify-content: space-between;">
                <label for="clear-history-toggle" style="margin: 0; font-size: 14px; color: var(--text-secondary);">每次进入页面时清空历史对话</label>
                <label class="switch">
                    <input type="checkbox" id="clear-history-toggle" checked>
                    <span class="slider"></span>
                </label>
            </div>
        </div>
    </div>

    <!-- 高级设置区域 -->
    <div class="divider" style="display: none;"></div>

    <div id="advanced-settings" class="hidden" style="display: none;">
        <!-- ASR模型选择 -->
        <div id="mic_mode_div" class="control-section">
            <h4>选择ASR模型模式</h4>
            <div class="radio-group">
                <div class="radio-option">
                    <input name="asr_mode" type="radio" value="2pass" id="2pass-radio" checked>
                    <label for="2pass-radio">2pass</label>
                </div>
                <div class="radio-option">
                    <input name="asr_mode" type="radio" value="online" id="online-radio">
                    <label for="online-radio">Online</label>
                </div>
                <div class="radio-option">
                    <input name="asr_mode" type="radio" value="offline" id="offline-radio">
                    <label for="offline-radio">Offline</label>
                </div>
            </div>
        </div>

        <!-- 文件上传 -->
        <div id="rec_mode_div" class="control-section hidden">
            <h4>选择音频文件</h4>
            <div class="file-upload">
                <label for="upfile" class="file-upload-label">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 8px; vertical-align: text-bottom;">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                        <polyline points="17 8 12 3 7 8"/>
                        <line x1="12" y1="3" x2="12" y2="15"/>
                    </svg>
                    选择音频文件
                </label>
                <input type="file" id="upfile" accept="audio/*">
            </div>
        </div>

        <!-- ITN选项 -->
        <div id="use_itn_div" class="control-section">
            <h4>反向文本归一化 (ITN)</h4>
            <div class="radio-group">
                <div class="radio-option">
                    <input name="use_itn" type="radio" value="false" id="itn-false" checked>
                    <label for="itn-false">否</label>
                </div>
                <div class="radio-option">
                    <input name="use_itn" type="radio" value="true" id="itn-true">
                    <label for="itn-true">是</label>
                </div>
            </div>
        </div>


        <!-- 热词 -->
        <div class="control-section hidden">
            <h4>热词设置</h4>
            <p style="margin: 0 0 8px 0; color: var(--text-secondary); font-size: 13px;">每行一个关键词，空格分隔权重，例如 "阿里巴巴 20"</p>
            <textarea id="varHot" rows="3">阿里巴巴 20
hello world 40</textarea>
        </div>
    </div>

    <!-- 显示/隐藏高级设置按钮 -->
    <button id="toggle-advanced" style="display: none; width: 100%; background-color: var(--background-dark); color: var(--text-primary);">
        显示高级设置
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-left: 8px; vertical-align: text-bottom;">
            <polyline points="6 9 12 15 18 9"></polyline>
        </svg>
    </button>
</div>

<script src="config.js" charset="UTF-8"></script>
<script src="recorder-core.js" charset="UTF-8"></script>
<script src="wav.js" charset="UTF-8"></script>
<script src="pcm.js" charset="UTF-8"></script>
<script src="wsconnecter.js" charset="utf-8"></script>
<script src="main.js" charset="utf-8"></script>
<!--<script src="../client.js"></script>-->
<script>
    // 高级设置切换
    document.getElementById('toggle-advanced').addEventListener('click', function() {
        const advancedSettings = document.getElementById('advanced-settings');
        const isHidden = advancedSettings.classList.contains('hidden');

        if (isHidden) {
            advancedSettings.classList.remove('hidden');
            this.innerHTML = `
                    隐藏高级设置
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-left: 8px; vertical-align: text-bottom;">
                        <polyline points="18 15 12 9 6 15"></polyline>
                    </svg>
                `;
        } else {
            advancedSettings.classList.add('hidden');
            this.innerHTML = `
                    显示高级设置
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-left: 8px; vertical-align: text-bottom;">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                `;
        }
    });

    // WebSocket相关变量
    window.socket = null;
    window.userId = "user_id"; // 固定用户ID
    window.selectedClientId = null;

    // 从URL参数或上一个页面获取客户机ID
    function getClientIdFromPreviousPage() {
        // 首先尝试从sessionStorage获取（index.html中存储的数据）
        const sessionClientId = sessionStorage.getItem('selectedClientId');
        if (sessionClientId) {
            console.log('从sessionStorage获取到客户机ID:', sessionClientId);
            // 获取数字人详细信息
            try {
                const digitalHumanStr = sessionStorage.getItem('selectedDigitalHuman');
                if (digitalHumanStr) {
                    window.selectedDigitalHuman = JSON.parse(digitalHumanStr);
                    console.log('从sessionStorage获取到数字人信息:', window.selectedDigitalHuman);
                }
            } catch (e) {
                console.error('解析数字人信息失败:', e);
            }
            return sessionClientId;
        }
        
        // 尝试从URL参数获取
        const urlParams = new URLSearchParams(window.location.search);
        const clientId = urlParams.get('clientId');
        
        // 如果URL中有参数，则使用URL参数
        if (clientId) {
            return clientId;
        }
        
        // 如果没有URL参数，则尝试获取上一个页面传递的数据
        if (window.opener && window.opener.selectedClientId) {
            return window.opener.selectedClientId;
        }
        
        // 如果是从localStorage中获取
        const storedClientId = localStorage.getItem('selectedClientId');
        if (storedClientId) {
            return storedClientId;
        }
        
        // 回退到window对象中的全局变量
        if (window.initialClientId) {
            return window.initialClientId;
        }
        
        return null;
    }

    // DOM元素引用
    const currentClientElement = document.getElementById('current-client');
    const messageContainer = document.getElementById('message-container');

    // 初始化按钮状态
    document.addEventListener('DOMContentLoaded', function() {
        // 获取客户机ID
        window.selectedClientId = getClientIdFromPreviousPage();
        
        // 尝试从localStorage获取数字人信息（如果从sessionStorage没有获取到）
        if (!window.selectedDigitalHuman) {
            try {
                const savedDigitalHumanStr = localStorage.getItem('selectedDigitalHuman');
                if (savedDigitalHumanStr) {
                    window.selectedDigitalHuman = JSON.parse(savedDigitalHumanStr);
                    console.log('从localStorage获取到数字人信息:', window.selectedDigitalHuman);
                }
            } catch (e) {
                console.error('从localStorage获取数字人信息失败:', e);
            }
        }
        
        // 将客户机ID存入localStorage以便日后使用
        if (window.selectedClientId) {
            try {
                localStorage.setItem('selectedClientId', window.selectedClientId);
            } catch (e) {
                console.error('保存客户机ID到localStorage失败:', e);
            }
            
            // 清除sessionStorage，防止下次加载时重复使用
            setTimeout(() => {
                sessionStorage.removeItem('selectedClientId');
                sessionStorage.removeItem('selectedDigitalHuman');
                console.log('已清除sessionStorage中的临时数据');
            }, 1000);
        }
        
        if (window.selectedClientId) {
            // 获取数字人名称
            let digitalHumanName = '未知数字人';
            if (window.selectedDigitalHuman && window.selectedDigitalHuman.name) {
                digitalHumanName = window.selectedDigitalHuman.name;
                
                // 保存数字人信息到localStorage，以便页面刷新后能够恢复
                try {
                    localStorage.setItem('selectedDigitalHuman', JSON.stringify(window.selectedDigitalHuman));
                } catch (e) {
                    console.error('保存数字人信息失败:', e);
                }
            }
            
            // 更新数字人显示名称
            if (currentClientElement) {
                currentClientElement.textContent = digitalHumanName;
            }
            
            // 更新连接信息提示
                const infoDiv = document.getElementById('info_div');
                if (infoDiv) {
                infoDiv.innerHTML = `<span style="color:#4361ee">🔄 正在连接到数字人: ${digitalHumanName}...</span>`;
                }
            } else {
                const infoDiv = document.getElementById('info_div');
                if (infoDiv) {
                infoDiv.innerHTML = `<span style="color:#f72585">❌ 未找到数字人ID，正在返回...</span>`;
            }
            
            // 显示错误消息
            window.addMessage('系统', '未找到数字人ID，正在返回选择页面', 'system');
            
            // 延迟1秒后跳转到index.html
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1000);
            return;
        }

        // 按住说话按钮事件处理
        const btnPressAndHold = document.getElementById('btnPressAndHold');
        if (btnPressAndHold) {
            btnPressAndHold.addEventListener('mousedown', function() {
                // 按下时连接并开始识别
                let connResult = start();
                if (connResult === 1) {
                    setTimeout(function() {
                        record();
                    }, 500); // 给WebSocket连接一点时间建立
                    this.classList.add('recording');
                }
            });

            btnPressAndHold.addEventListener('mouseup', function() {
                // 松开时停止识别，并发送消息
                if (this.classList.contains('recording')) {
                    window.stopASR();
                    // 获取文本输入区域的内容
                    const varAreaText = document.getElementById('varArea').value.trim();
                    // 只有当有识别结果时才发送
                    if (varAreaText !== "") {
                        sendTextMessage(varAreaText, window.messageType);
                    }
                    this.classList.remove('recording');
                }
            });

            // 处理触摸事件，适用于移动设备
            btnPressAndHold.addEventListener('touchstart', function(e) {
                e.preventDefault(); // 防止触发其他事件
                let connResult = start();
                if (connResult === 1) {
                    setTimeout(function() {
                        record();
                    }, 500); // 给WebSocket连接一点时间建立
                    this.classList.add('recording');
                }
            });

            btnPressAndHold.addEventListener('touchend', function(e) {
                e.preventDefault(); // 防止触发其他事件
                if (this.classList.contains('recording')) {
                    window.stopASR();
                    // 获取文本输入区域的内容
                    const varAreaText = document.getElementById('varArea').value.trim();
                    // 只有当有识别结果时才发送
                    if (varAreaText !== "") {
                        sendTextMessage(varAreaText, window.messageType);
                    }
                    this.classList.remove('recording');
                }
            });
        }

        // 处理清空历史记录选项
        const clearHistoryToggle = document.getElementById('clear-history-toggle');
        if (clearHistoryToggle) {
            // 从localStorage加载保存的设置
            try {
                const savedSetting = localStorage.getItem('shouldClearHistoryOnLoad');
                if (savedSetting !== null) {
                    const shouldClear = savedSetting === 'true';
                    clearHistoryToggle.checked = shouldClear;
                    window.shouldClearHistoryOnLoad = shouldClear;
                }
            } catch (e) {
                console.error('加载清空历史设置失败:', e);
            }

            // 监听变化
            clearHistoryToggle.addEventListener('change', function() {
                const shouldClear = this.checked;
                // 调用全局函数设置
                if (window.setClearHistoryOnLoad) {
                    window.setClearHistoryOnLoad(shouldClear);
                } else {
                    window.shouldClearHistoryOnLoad = shouldClear;
                    try {
                        localStorage.setItem('shouldClearHistoryOnLoad', shouldClear);
                    } catch (e) {
                        console.error('保存清空历史设置失败:', e);
                    }
                }

                console.log(`已设置页面加载时${shouldClear ? '清空' : '保留'}历史对话消息`);
            });
        }

        // 立即连接到服务器
        window.connectToServer(window.userId);

        // 添加文本发送按钮事件处理
        const sendTextBtn = document.getElementById('send-text-btn');
        const textInput = document.getElementById('text-input');

        // 消息类型按钮处理
        const echoBtn = document.getElementById('echo-btn');
        const chatBtn = document.getElementById('chat-btn');
        
        // 初始化消息类型
        window.messageType = 'echo';
        
        // 切换消息类型按钮状态
        function setMessageType(type) {
            window.messageType = type;
            
            if (type === 'echo') {
                echoBtn.classList.add('active');
                chatBtn.classList.remove('active');
            } else {
                echoBtn.classList.remove('active');
                chatBtn.classList.add('active');
            }
            
            console.log(`已切换到${type}模式`);
        }
        
        // 消息类型按钮点击事件
        if (echoBtn && chatBtn) {
            echoBtn.addEventListener('click', function() {
                setMessageType('echo');
            });
            
            chatBtn.addEventListener('click', function() {
                setMessageType('chat');
            });
        }

        if (sendTextBtn && textInput) {
            sendTextBtn.addEventListener('click', function() {
                const text = textInput.value.trim();
                if (text) {
                    // 发送文本消息
                    window.sendTextMessage(text, window.messageType);
                    // 清空输入框
                    textInput.value = '';
                }
            });

            // 添加回车键发送功能
            textInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendTextBtn.click();
                }
            });
        }

        // 媒体上传功能初始化
        initMediaUpload();
    });

    // 媒体上传功能
    function initMediaUpload() {
        const uploadBtn = document.getElementById('upload-media-btn');
        const mediaInput = document.getElementById('media-upload');
        const mediaStatusDiv = document.getElementById('media-upload-status');
        const textInputArea = document.getElementById('text-input');
        
        // 当前上传的媒体信息
        let currentMediaInfo = null;
        // 记录上传状态
        let isUploading = false;
        
        // 添加页面刷新/离开警告
        window.addEventListener('beforeunload', function(e) {
            if (isUploading) {
                // 显示确认对话框
                const confirmationMessage = '文件正在上传中，离开页面将取消上传。确定要离开吗？';
                e.returnValue = confirmationMessage;
                return confirmationMessage;
            }
        });
        
        if (uploadBtn && mediaInput) {
            // 点击上传按钮触发文件选择
            uploadBtn.addEventListener('click', function() {
                mediaInput.click();
            });
            
            // 添加触摸设备支持
            uploadBtn.addEventListener('touchend', function(e) {
                e.preventDefault(); // 防止双击缩放
                mediaInput.click();
            });
            
            // 监听文件选择
            mediaInput.addEventListener('change', function(e) {
                if (this.files && this.files.length > 0) {
                    const file = this.files[0];
                    uploadMedia(file);
                    // 重置input以便再次选择同一文件
                    this.value = '';
                }
            });
            
            // 添加拖放支持（PC端）
            if (textInputArea) {
                // 拖动进入时的样式变化
                textInputArea.addEventListener('dragenter', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.style.borderColor = 'var(--primary-color)';
                    this.style.backgroundColor = 'rgba(67, 97, 238, 0.05)';
                });
                
                // 拖动悬停时
                textInputArea.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                });
                
                // 拖动离开时恢复样式
                textInputArea.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.style.borderColor = 'var(--border-color)';
                    this.style.backgroundColor = '';
                });
                
                // 处理拖放的文件
                textInputArea.addEventListener('drop', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    this.style.borderColor = 'var(--border-color)';
                    this.style.backgroundColor = '';
                    
                    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
                        const file = e.dataTransfer.files[0];
                        uploadMedia(file);
                    }
                });
            }
            
            // 上传媒体文件
            function uploadMedia(file) {
                // 重置当前媒体信息
                currentMediaInfo = null;
                
                // 检查文件大小限制
                const maxSizeMB = 50; // 最大50MB
                if (file.size > maxSizeMB * 1024 * 1024) {
                    showMediaStatus(`文件过大，请上传小于${maxSizeMB}MB的文件`, 'error');
                    return;
                }
                
                // 确定媒体类型
                let mediaType = '';
                if (file.type.startsWith('image/')) {
                    mediaType = 'image';
                } else if (file.type.startsWith('video/')) {
                    mediaType = 'video';
                } else if (file.type.startsWith('audio/')) {
                    mediaType = 'audio';
                } else {
                    showMediaStatus('不支持的文件类型，请上传图片、视频或音频文件', 'error');
                    return;
                }
                
                // 设置上传状态
                isUploading = true;
                
                // 显示上传状态和进度条
                showMediaStatus(`<div>
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>正在上传${getMediaTypeName(mediaType)}...</span>
                        <span id="upload-progress-text">0%</span>
                    </div>
                    <div style="width: 100%; height: 4px; background-color: #f0f0f0; border-radius: 2px; margin-top: 8px; overflow: hidden;">
                        <div id="upload-progress-bar" style="width: 0%; height: 100%; background-color: var(--primary-color); transition: width 0.2s;"></div>
                    </div>
                </div>`, 'loading');
                
                // 创建FormData对象
                const formData = new FormData();
                formData.append('file', file);
                
                // 使用XMLHttpRequest以支持进度监控
                const xhr = new XMLHttpRequest();
                
                // 进度事件
                xhr.upload.addEventListener('progress', function(e) {
                    if (e.lengthComputable) {
                        const percentComplete = Math.round((e.loaded / e.total) * 100);
                        const progressBar = document.getElementById('upload-progress-bar');
                        const progressText = document.getElementById('upload-progress-text');
                        
                        if (progressBar) {
                            progressBar.style.width = percentComplete + '%';
                        }
                        
                        if (progressText) {
                            progressText.textContent = percentComplete + '%';
                        }
                    }
                });
                
                // 完成事件
                xhr.addEventListener('load', function() {
                    // 更新上传状态
                    isUploading = false;
                    
                    if (xhr.status >= 200 && xhr.status < 300) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            
                            if (response.success) {
                                // 保存媒体信息
                                currentMediaInfo = {
                                    type: mediaType,
                                    url: response.full_url,
                                    fileName: response.file_name,
                                    fileUrl: response.file_url,
                                    originalName: response.original_filename
                                };
                                
                                // 添加媒体预览
                                let previewHtml = '';
                                if (mediaType === 'image') {
                                    previewHtml = `<div class="media-preview-container"><img src="${response.full_url}" alt="${response.original_filename}"></div>`;
                                } else if (mediaType === 'video') {
                                    previewHtml = `<div class="media-preview-container"><video src="${response.full_url}" controls style="max-width: 100%; max-height: 150px; border-radius: 8px;"></video></div>`;
                                } else if (mediaType === 'audio') {
                                    previewHtml = `<div class="media-preview-container"><audio src="${response.full_url}" controls style="width: 100%;"></audio></div>`;
                                }
                                // 显示上传成功状态以及发送按钮
                                showMediaStatus(
                                    `<div>
                                        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                                            <span style="margin-bottom: 5px;">${getMediaTypeName(mediaType)}上传成功: ${response.original_filename}</span>
                                            <div style="margin-bottom: 5px; margin-left: auto; text-align: right;">
                                                <button id="send-media-btn" style="margin-left: 10px; padding: 6px 10px; background-color: var(--primary-color); color: white; border: none; border-radius: 15px; cursor: pointer; font-size: 12px;">
                                                    发送${getMediaTypeName(mediaType)}
                                                </button>
                                                <button id="cancel-media-btn" style="margin-left: 5px; padding: 6px 10px; background-color: var(--background-light); color: var(--text-secondary); border: 1px solid var(--border-color); border-radius: 15px; cursor: pointer; font-size: 12px;">
                                                    取消
                                                </button>
                                            </div>
                                        </div>
                                        ${previewHtml}
                                    </div>`,
                                    'success'
                                );
                                
                                // 添加发送媒体按钮事件
                                const sendMediaBtn = document.getElementById('send-media-btn');
                                if (sendMediaBtn) {
                                    sendMediaBtn.addEventListener('click', function() {
                                        if (currentMediaInfo) {
                                            // 发送媒体消息
                                            sendMediaMessage(currentMediaInfo);
                                            // 清除媒体信息和状态
                                            showMediaStatus('', '');
                                            currentMediaInfo = null;
                                        }
                                    });
                                }
                                
                                // 添加取消按钮事件
                                const cancelMediaBtn = document.getElementById('cancel-media-btn');
                                if (cancelMediaBtn) {
                                    cancelMediaBtn.addEventListener('click', function() {
                                        showMediaStatus('', '');
                                        currentMediaInfo = null;
                                    });
                                }
                            } else {
                                showMediaStatus(`上传失败: ${response.message || '未知错误'}`, 'error');
                            }
                        } catch (e) {
                            console.error('解析响应数据失败:', e);
                            showMediaStatus('解析响应数据失败', 'error');
                        }
                    } else {
                        showMediaStatus(`上传失败: ${xhr.status} ${xhr.statusText}`, 'error');
                    }
                });
                
                // 错误事件
                xhr.addEventListener('error', function() {
                    isUploading = false;
                    showMediaStatus('网络错误，上传失败', 'error');
                });
                
                // 取消事件
                xhr.addEventListener('abort', function() {
                    isUploading = false;
                    showMediaStatus('上传已取消', 'warning');
                });
                
                // 打开请求
                xhr.open('POST', `${window.host}/upload/${mediaType}`);
                
                // 发送请求
                xhr.send(formData);
            }
            
            // 发送媒体消息
            function sendMediaMessage(mediaInfo) {
                if (!mediaInfo || !mediaInfo.url) return;
                
                // 使用sendTextMessage发送媒体URL，mediaType作为类型
                window.sendTextMessage(mediaInfo.url, mediaInfo.type);
                
                // 显示发送成功提示
                showMediaStatus(`${getMediaTypeName(mediaInfo.type)}已发送`, 'success');
                setTimeout(() => {
                    if (mediaStatusDiv) {
                        mediaStatusDiv.style.display = 'none';
                    }
                }, 3000);
            }
            
            // 显示媒体上传状态
            function showMediaStatus(message, status) {
                if (!mediaStatusDiv) return;
                
                if (!message) {
                    mediaStatusDiv.style.display = 'none';
                    return;
                }
                
                let statusColor = '';
                let icon = '';
                
                switch(status) {
                    case 'loading':
                        statusColor = '#4361ee'; // 蓝色
                        icon = '<span style="display: inline-block; margin-right: 5px;">🔄</span>';
                        break;
                    case 'success':
                        statusColor = '#2ec4b6'; // 绿色
                        icon = '<span style="display: inline-block; margin-right: 5px;">✅</span>';
                        break;
                    case 'error':
                        statusColor = '#f72585'; // 红色
                        icon = '<span style="display: inline-block; margin-right: 5px;">❌</span>';
                        break;
                    default:
                        statusColor = '#4361ee'; // 默认蓝色
                }
                
                // 如果消息中没有HTML标签，添加图标
                if (!message.includes('<')) {
                    message = icon + message;
                }
                
                mediaStatusDiv.innerHTML = message;
                mediaStatusDiv.style.color = statusColor;
                mediaStatusDiv.style.display = 'block';
            }
            
            // 获取媒体类型名称
            function getMediaTypeName(type) {
                switch(type) {
                    case 'image':
                        return '图片';
                    case 'video':
                        return '视频';
                    case 'audio':
                        return '音频';
                    default:
                        return '媒体';
                }
            }
        }
    }

    const statusElement = document.querySelector('.status');

    // 连接到服务器
    window.connectToServer = function(userId) {
        // 使用固定用户ID而不是参数（参数可能是null或undefined）
        userId = userId || "远程语音";
        
        // 构建WebSocket URL
        const wsUrl = `${window.wsProtocol}://${window.ochost}/ws/user/${userId}`;
        
        console.log('尝试连接到WebSocket服务器:', wsUrl);

        // 关闭现有连接
        if (window.socket && window.socket.readyState === WebSocket.OPEN) {
            window.socket.close();
        }

        window.socket = new WebSocket(wsUrl);

        window.socket.onopen = () => {
            console.log('WebSocket已连接到服务器');
            if (statusElement) {
                statusElement.textContent = '状态: 已连接';
                statusElement.className = 'status status-connected';
            }

            // 更新连接指示器颜色 - 绿色表示已连接
            const connectionIndicator = document.getElementById('connection-indicator');
            if (connectionIndicator) {
                connectionIndicator.style.backgroundColor = '#2ec4b6'; // 绿色
            }

            const infoDiv = document.getElementById('info_div');
            if (infoDiv) {
                infoDiv.innerHTML = `<span style="color:#4361ee">🔄 已连接服务器，正在选择数字人...</span>`;
            }

            // 添加连接成功消息
            window.addMessage('系统', '已连接到服务器', 'system');

            // 连接成功后，直接选择客户机（如果有）
            if (window.selectedClientId) {
                console.log(`直接选择数字人: ${window.selectedClientId}`);
                
                // 延迟100ms发送选择命令，确保WebSocket连接已完全建立
                setTimeout(() => {
                    if (window.socket && window.socket.readyState === WebSocket.OPEN) {
                        window.socket.send(JSON.stringify({ select_client: window.selectedClientId }));
                        window.addMessage('系统', `正在尝试连接到数字人: ${window.selectedClientId}`, 'system');
                    } else {
                        console.error('WebSocket连接已关闭，无法选择数字人');
                        window.addMessage('系统', 'WebSocket连接已关闭，无法选择数字人', 'system');
                    }
                }, 100);
            } else {
                window.addMessage('系统', '未找到数字人ID，无法自动连接', 'system');
            }
        };

        window.socket.onmessage = (event) => {
            const data = JSON.parse(event.data);
            console.log('收到WebSocket消息:', data);

            if (data.type === 'client_selected') {
                if (data.success) {
                    window.selectedClientId = data.client_id;
                    
                    // 获取数字人名称
                    let digitalHumanName = '未知数字人';
                    if (window.selectedDigitalHuman && window.selectedDigitalHuman.name) {
                        digitalHumanName = window.selectedDigitalHuman.name;
                        
                        // 保存数字人信息到localStorage，以便页面刷新后能够恢复
                        try {
                            localStorage.setItem('selectedDigitalHuman', JSON.stringify(window.selectedDigitalHuman));
                        } catch (e) {
                            console.error('保存数字人信息失败:', e);
                        }
                    }
                    
                    if (currentClientElement) currentClientElement.textContent = digitalHumanName;
                    
                    console.log(`已选择数字人: ${digitalHumanName} (ID: ${window.selectedClientId})`);

                    const infoDiv = document.getElementById('info_div');
                    if (infoDiv) {
                        // infoDiv.innerHTML = `<span style="color:#2ec4b6">✓ 已连接数字人 (ID: ${window.selectedClientId})，按住麦克风开始说话</span>`;
                        infoDiv.innerHTML = `<span style="color:#2ec4b6">✓ 已连接数字人，输入文本或按住麦克风开始对话</span>`;
                    }

                    // 添加选择成功消息
                    window.addMessage('系统', `已连接到数字人: ${digitalHumanName}`, 'system');
                    
                    // 显示按住说话按钮
                    document.querySelector('.press-hold-container').style.display = 'flex';
                } else {
                    console.error('选择数字人失败');

                    const infoDiv = document.getElementById('info_div');
                    if (infoDiv) {
                        infoDiv.innerHTML = `<span style="color:#f72585">❌ 选择数字人失败，正在返回...</span>`;
                    }

                    // 添加选择失败消息
                    window.addMessage('系统', '选择数字人失败，正在返回选择页面', 'system');
                    
                    // 隐藏按住说话按钮
                    document.querySelector('.press-hold-container').style.display = 'none';
                    
                    // 延迟1秒后跳转到index.html
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1000);
                }
            } else if (data.type === 'message_sent') {
                console.log(`消息已发送到数字人: ${data.client_id}`);
                // 添加消息发送成功信息
                window.addMessage('系统', `消息已发送到数字人: ${data.client_id}`, 'system');
            } else if (data.type === 'type_change_sent') {
                console.log(`类型变更消息已发送到数字人: ${data.client_id}`);
                // 添加类型变更成功消息
                window.addMessage('系统', `类型变更消息已发送到数字人: ${data.client_id}`, 'system');
            } else if (data.type === 'client_disconnected') {
                console.log(`数字人已断开连接: ${data.client_id}`);
                window.addMessage('系统', `数字人已断开连接: ${data.client_id}`, 'system');
                
                if (window.selectedClientId === data.client_id) {
                    window.selectedClientId = null;
                    
                    // 显示断开连接的消息
                    const infoDiv = document.getElementById('info_div');
                    if (infoDiv) {
                        infoDiv.innerHTML = `<span style="color:#f72585">⚠️ 数字人已断开连接，正在返回...</span>`;
                    }
                    
                    // 隐藏按住说话按钮
                    document.querySelector('.press-hold-container').style.display = 'none';
                    
                    // 显示错误消息
                    window.addMessage('系统', '数字人已断开连接，正在返回选择页面', 'system');
                    
                    // 延迟1秒后跳转到index.html
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1000);
                }
            } else if (data.type === 'error') {
                console.error(`WebSocket错误: ${data.message}`);

                const infoDiv = document.getElementById('info_div');
                if (infoDiv) {
                    infoDiv.innerHTML = `<span style="color:#f72585">❌ 错误: ${data.message}</span>`;
                }

                // 添加错误消息
                window.addMessage('错误', data.message, 'system');
            }
        };

        window.socket.onclose = () => {
            console.log('与服务器的连接已关闭');
            if (statusElement) {
                statusElement.textContent = '状态: 未连接';
                statusElement.className = 'status status-disconnected';
            }

            const connectionIndicator = document.getElementById('connection-indicator');
            if (connectionIndicator) {
                connectionIndicator.style.backgroundColor = '#f72585'; // 红色
            }

            window.selectedClientId = null;
            
            const infoDiv = document.getElementById('info_div');
            if (infoDiv) {
                infoDiv.innerHTML = `<span style="color:#ff9f1c">🔄 与服务器断开连接，正在尝试重连...</span>`;
            }
            
            // 隐藏按住说话按钮
            document.querySelector('.press-hold-container').style.display = 'none';

            // 添加断开连接消息
            window.addMessage('系统', '与服务器的连接已断开', 'system');

            // 自动重连
            setTimeout(() => {
                window.connectToServer(window.userId);
            }, 5000);

            // 延迟1秒后跳转到index.html
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1000);
        };

        window.socket.onerror = (error) => {
            console.error('WebSocket错误:', error);

            const infoDiv = document.getElementById('info_div');
            if (infoDiv) {
                infoDiv.innerHTML = `<span style="color:#f72585">❌ 连接错误，请检查网络</span>`;
            }

            // 添加错误消息
            window.addMessage('系统', '连接错误，请检查网络', 'system');
        };
    }

    // 添加消息到消息容器
    window.addMessage = function(sender, text, type) {
        const messageContainer = document.getElementById('message-container');
        if (!messageContainer) return;

        // 创建现在的时间
        const now = new Date();
        const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;

        const messageElement = document.createElement('div');
        messageElement.className = `message ${type}`;

        if (type === 'system') {
            messageElement.innerHTML = `<div class="message-content">${text}</div><div class="message-time">${timeStr}</div>`;
        } else {
            messageElement.innerHTML = `<div class="message-sender"><strong>${sender}</strong></div><div class="message-content">${text}</div><div class="message-time">${timeStr}</div>`;
        }

        messageContainer.appendChild(messageElement);
        messageContainer.scrollTop = messageContainer.scrollHeight;

        console.log(`${timeStr} - ${sender}: ${text} (${type})`);
    }

    // 发送文本消息
    // window.sendTextMessage = function(text, type = 'echo') {
    //     if (!window.socket || window.socket.readyState !== WebSocket.OPEN) {
    //         console.error('无法发送消息：WebSocket未连接');
    //         window.addMessage('系统', '未连接到服务器，无法发送消息', 'system');
    //         return;
    //     }
    //
    //     if (!window.selectedClientId) {
    //         console.error('无法发送消息：未选择数字人');
    //         window.addMessage('系统', '未选择数字人，无法发送消息', 'system');
    //         return;
    //     }
    //
    //     const message = {
    //         send_message: {
    //
    //             message: text,
    //             type: type
    //         }
    //     };
    //
    //     window.socket.send(JSON.stringify(message));
    //     window.addMessage('您', text, 'sent');
    //     console.log(`发送消息到数字人 ${window.selectedClientId}: ${text} (${type})`);
    // }
</script>
</body>
</html>