<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>语音识别</title>
    <script src="ws-bridge.js"></script>
    <style>
        :root {
            --primary-color: #4361ee;
            --primary-hover: #3a56d4;
            --secondary-color: #f72585;
            --text-primary: #333;
            --text-secondary: #666;
            --background-light: #ffffff;
            --background-dark: #f8f9fa;
            --border-color: #e0e0e0;
            --success-color: #2ec4b6;
            --warning-color: #ff9f1c;
            --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            --border-radius: 12px;
            --transition: all 0.3s ease;
        }

        /* 全局盒模型设置 */
        *, *::before, *::after {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Se<PERSON><PERSON> UI', <PERSON><PERSON>, <PERSON>xy<PERSON>, Ubuntu, Cantarell, sans-serif;
            margin: 0;
            padding: 10px;
            background: transparent;
            color: var(--text-primary);
            font-size: 14px;
            /*line-height: 1.5;*/
        }

        .asr-container {
            width: 100%;
            border-radius: var(--border-radius);
            max-width: 500px;
            margin: 0 auto;
            /*overflow: hidden;*/
        }

        .control-section {
            margin-bottom: 16px;
        }

        .hidden {
            display: none;
        }

        /* 输入框样式 */
        input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            box-sizing: border-box;
            font-size: 14px;
            transition: var(--transition);
        }

        input[type="text"]:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        }

        /* 结果显示区域样式 */
        #varArea {
            width: 95%;
            /*height: 130px;*/
            padding: 12px;
            margin-bottom: 16px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            /*resize: none;*/
            font-family: inherit;
            font-size: 16px;
            background-color: var(--background-light);
            transition: var(--transition);
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        #varArea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        }

        /* 状态提示 */
        #info_div {
            margin: 15px 0;
            font-weight: 500;
            color: var(--text-primary);
            text-align: center;
            padding: 10px;
            border-radius: 20px;
            background-color: rgba(67, 97, 238, 0.1);
        }

        /* 按钮区域 */
        .button-group {
            display: flex;
            justify-content: space-between;
            gap: 10px;
            margin-bottom: 16px;
        }

        button {
            padding: 10px 16px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: var(--transition);
            min-width: 80px;
        }

        button:hover {
            background-color: var(--primary-hover);
            transform: translateY(-1px);
        }

        button:active {
            transform: translateY(0);
        }

        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
            transform: none;
        }

        #btnStart {
            background-color: var(--success-color);
        }

        #btnStart:hover {
            background-color: #25b0a3;
        }

        #btnStop {
            background-color: var(--warning-color);
        }

        #btnStop:hover {
            background-color: #f59000;
        }

        /* 音频播放器 */
        audio {
            width: 100%;
            height: 40px;
            border-radius: 8px;
            margin-top: 8px;
            display: none; /* 隐藏音频播放器 */
        }

        /* 单选按钮组样式 */
        .radio-group {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 16px;
        }

        .radio-option {
            display: flex;
            align-items: center;
            cursor: pointer;
        }

        .radio-option input[type="radio"] {
            appearance: none;
            -webkit-appearance: none;
            width: 18px;
            height: 18px;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            margin-right: 8px;
            display: grid;
            place-content: center;
            transition: var(--transition);
        }

        .radio-option input[type="radio"]::before {
            content: "";
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background-color: var(--primary-color);
            transform: scale(0);
            transition: var(--transition);
        }

        .radio-option input[type="radio"]:checked {
            border-color: var(--primary-color);
        }

        .radio-option input[type="radio"]:checked::before {
            transform: scale(1);
        }

        .radio-option label {
            font-size: 14px;
            color: var(--text-primary);
            cursor: pointer;
        }

        /* 文件上传样式 */
        .file-upload {
            position: relative;
            overflow: hidden;
            display: inline-block;
            width: 100%;
        }

        .file-upload-label {
            display: block;
            padding: 10px 16px;
            background-color: var(--background-dark);
            color: var(--text-primary);
            border: 1px dashed var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            text-align: center;
            transition: var(--transition);
        }

        .file-upload-label:hover {
            background-color: #f0f0f0;
        }

        .file-upload input[type="file"] {
            font-size: 100px;
            opacity: 0;
            position: absolute;
            right: 0;
            top: 0;
            cursor: pointer;
        }

        /* 热词输入框 */
        #varHot {
            width: 100%;
            height: 80px;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            resize: vertical;
            font-family: inherit;
            font-size: 14px;
            transition: var(--transition);
        }

        #varHot:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        }

        /* 分割线 */
        .divider {
            height: 1px;
            background-color: var(--border-color);
            margin: 16px 0;
        }

        /* 标题样式 */
        h4 {
            margin: 0 0 12px 0;
            color: var(--text-primary);
            font-weight: 600;
            font-size: 15px;
        }

        /* 开关样式 */
        .switch-container {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
        }

        .switch-container label {
            margin: 0 10px 0 0;
            cursor: pointer;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 44px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:focus + .slider {
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        }

        input:checked + .slider:before {
            transform: translateX(20px);
        }

        /* 按住说话按钮容器 */
        .press-hold-container {
            display: flex;
            justify-content: center;
            margin: 15px 0;
        }

        /* 按住说话按钮样式 */
        .press-hold-button {
            width: 100px;
            height: 100px;
            padding: 0;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            /* display: flex; */
            align-items: center;
            justify-content: center;
        }

        .press-hold-button svg {
            width: 40px;
            height: 40px;
        }

        .press-hold-button:hover {
            background-color: var(--primary-hover);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
        }

        .press-hold-button.recording {
            background-color: var(--secondary-color);
            transform: scale(0.95);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        /* 用户连接区域样式 */
        .container {
            border: 1px solid #ddd;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .input-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        select {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 10px;
            box-sizing: border-box;
            font-size: 16px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 16px;
        }

        select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
        }

        select option {
            padding: 10px;
            font-size: 16px;
        }

        .status {
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
        }

        .status-connected {
            color: var(--success-color);
        }

        .status-disconnected {
            color: var(--secondary-color);
        }

        /* 客户机选择区域 */
        .current-client-info {
            background-color: rgba(46, 196, 182, 0.1);
            padding: 12px;
            border-radius: 10px;
            text-align: center;
            margin-top: 10px;
            border: 1px solid rgba(46, 196, 182, 0.2);
        }

        /* 数字人网格样式 */
        .client-list-container {
            width: 100%;
            max-height: 400px;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            background-color: white;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            margin-bottom: 15px;
        }

        .digital-human-grid {
            width: 100%;
            max-height: 400px;
            overflow-y: auto;
            padding: 10px;
            display: grid;
            grid-template-columns: repeat(3, 1fr); /* 改为固定3列布局 */
            grid-gap: 12px; /* 减小间距 */
            -webkit-overflow-scrolling: touch;
            box-sizing: border-box; /* 确保内边距包含在宽度内 */
        }

        .digital-human-card {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            animation: fadeIn 0.5s ease;
            width: 100%;
            margin: 0 auto;
        }

        .digital-human-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .digital-human-card.selected {
            border: 3px solid var(--primary-color);
            box-shadow: 0 0 15px rgba(67, 97, 238, 0.3);
        }

        .digital-human-image-container {
            position: relative;
            padding-top: 177.77%; /* 9:16比例 */
            overflow: hidden;
        }
        
        .digital-human-image {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .digital-human-card:hover .digital-human-image {
            transform: scale(1.05);
        }

        .digital-human-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 8px;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
            color: white;
        }

        .digital-human-name {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 2px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
        }

        .digital-human-description {
            font-size: 12px;
            opacity: 0.85;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            transition: opacity 0.3s ease;
        }

        .digital-human-card:hover .digital-human-description {
            opacity: 1;
        }

        .loading-indicator {
            grid-column: 1 / -1;
            text-align: center;
            padding: 30px;
            color: #666;
            font-size: 14px;
        }

        @keyframes shimmer {
            0% { background-position: -468px 0 }
            100% { background-position: 468px 0 }
        }

        .digital-human-card-skeleton {
            height: 200px;
            border-radius: 8px;
            background: #f6f7f8;
            background: linear-gradient(to right, #f6f7f8 8%, #edeef1 18%, #f6f7f8 33%);
            background-size: 800px 104px;
            animation: shimmer 1.5s infinite linear;
        }

        @media (max-width: 480px) {
            .digital-human-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* 媒体查询优化手机端显示 */
        @media (max-width: 480px) {
            body {
                padding: 8px;
            }
            
            .container {
                padding: 12px;
                margin-bottom: 15px;
            }
            
            h4 {
                font-size: 16px;
                margin-bottom: 10px;
            }
            
            .button-group {
                flex-direction: row;
            }
            
            /* button {
                min-width: 0;
                flex: 1;
                padding: 10px 5px;
                font-size: 13px;
            } */
            
            /* .press-hold-button {
                width: 80px;
                height: 80px;
            }
            
            .press-hold-button svg {
                width: 30px;
                height: 30px;
            } */
            
            select {
                padding: 10px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
<div class="asr-container">
    <!-- 隐藏的配置项 -->
    <input id="wssip" type="text" onchange="addresschange()" value="wss://www.funasr.com:10096/" class="hidden">
    <a id="wsslink" href="#" class="hidden" onclick="window.open('https://*************:10095/', '_blank')">
        <div id="info_wslink">点击此处手动授权 wss://*************:10095/</div>
    </a>

    <!-- 用户连接区域 -->
    <div class="container" id="connection-section" style="padding: 15px;">
        <h4 style="text-align: center; margin-bottom: 15px; font-size: 18px; color: var(--primary-color);">数字人远程语音控制</h4>
        <div class="status status-disconnected" style="display: none; text-align: center; font-size: 14px; margin-bottom: 15px;">状态: 未连接</div>
        
        <div id="login-section" style="display: none;">
            <div class="input-group">
                <label for="user-id">用户ID:</label>
                <input type="text" id="user-id" placeholder="输入你的用户ID" value="远程语音">
            </div>
            <div class="button-group">
                <button id="connect-btn">连接</button>
                <button id="disconnect-btn" disabled>断开连接</button>
            </div>
        </div>
        
        <!-- 客户机选择区域 -->
        <div id="client-selection-section">
            <h4 style="font-size: 15px; text-align: center; margin-bottom: 10px;">选择接收端数字人形象</h4>
            
            <!-- 数字人形象网格滚动列表 -->
            <div class="client-list-container">
                <div id="digital-human-grid" class="digital-human-grid">
                    <!-- 数字人形象卡片将通过JS动态添加 -->
                    <div class="loading-indicator">加载数字人列表中...</div>
                </div>
            </div>
            
            <div style="margin: 15px 0 10px; display: flex; align-items: center; justify-content: space-between;">
                <button id="start-conversation-btn" style="flex: 1; margin-right: 10px; display: none; background-color: var(--primary-color); color: white; border: none; border-radius: 20px; padding: 12px 15px; font-size: 15px; font-weight: 500; transition: all 0.2s ease;">开始对话</button>
                <button id="refresh-btn" style="width: 40px; height: 40px; min-width: 40px; display: flex; align-items: center; justify-content: center; background-color: #f0f4f8; border: none; border-radius: 50%; padding: 0;">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="color: var(--primary-color);">
                        <polyline points="1 4 1 10 7 10"></polyline>
                        <polyline points="23 20 23 14 17 14"></polyline>
                        <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
                    </svg>
                </button>
            </div>
            
            <div class="current-client-info" style="text-align: center; padding: 12px; border-radius: 20px; margin-top: 10px;">
                <div style="font-weight: 600; font-size: 14px;">当前选择: <span id="current-client-name" style="color: var(--primary-color); font-weight: bold;">无</span></div>
                <div style="display: none; font-size: 12px; color: #666;" id="current-client-id-display">ID: <span id="current-client" style="font-family: monospace;">无</span></div>
            </div>
        </div>
    </div>

    <div class="recoder_mode_div hidden" style="display: none;">
        <div class="radio-group">
            <div class="radio-option">
                <input name="recoder_mode" onclick="on_recoder_mode_change()" type="radio" value="mic" checked id="mic-radio">
                <label for="mic-radio">麦克风</label>
            </div>
            <div class="radio-option">
                <input name="recoder_mode" onclick="on_recoder_mode_change()" type="radio" value="file" id="file-radio">
                <label for="file-radio">文件</label>
            </div>
        </div>
    </div>
    <!-- 客户机状态显示 -->
    <div id="client-status-display" style="display: none; margin-bottom: 16px; padding: 8px; background-color: #f0f8ff; border-radius: 8px; border: 1px solid #d1e5f9;">
        <span>当前客户机状态: </span><span id="current-client-status">等待连接...</span>
    </div>
    <!-- 状态提示 -->
    <div id="info_div" style="display: none; margin: 15px 0; text-align: center; font-size: 14px; padding: 10px; border-radius: 20px; background-color: rgba(67, 97, 238, 0.05);">请选择数字人</div>
    <!-- 文本输入和发送区域 -->
    <div class="container"  style="display: none; margin-bottom: 15px; padding: 15px;">
        <div style="display: flex; flex-direction: column; gap: 10px;">
            <div style="display: flex; gap: 10px;">
                <input type="text" id="text-input" placeholder="请输入要发送的文本..." style="flex: 1; padding: 12px; border: 1px solid var(--border-color); border-radius: 20px; font-size: 14px;">
                <div style="display: flex; gap: 5px;">
                    <select id="message-type" style="padding: 10px; border: 1px solid var(--border-color); border-radius: 20px; background-color: var(--background-light); font-size: 14px;">
                        <option value="echo">复述</option>
                        <option value="chat">对话</option>
                    </select>
                    <button id="send-text-btn" style="padding: 10px 20px; background-color: var(--primary-color); color: white; border: none; border-radius: 20px; cursor: pointer; font-size: 14px; font-weight: 500;">发送</button>
                </div>
            </div>
        </div>
    </div>
    <!-- 结果区域 -->
    <div class="container" style="display: none;">

        <div class="control-section" style="margin-bottom: 20px;">
            <div style="display: flex; flex-direction: column; gap: 15px;">
                <div>
                    <h4 style="text-align: center; margin-bottom: 10px; font-size: 15px;">语音识别结果</h4>
                    <textarea id="varArea" readonly placeholder="识别结果将显示在这里..." style="height: auto; box-shadow: 0 2px 8px rgba(0,0,0,0.05);"></textarea>
                </div>

                <div style=" display: none;">
                    <h4 style="text-align: center; margin-bottom: 10px; font-size: 15px;">消息记录</h4>
                    <div id="message-container" class="message-container"></div>
                </div>
            </div>
        </div>
    </div>
    <!-- 按钮区域 -->
    <div class="button-group" style="display: none;">
        <button id="btnConnect" style="display: none;">连接</button>
        <button id="btnStart" style="display: none;">开始</button>
        <button id="btnStop" style="display: none;" disabled>停止</button>
    </div>
    <!-- 音频播放器 -->
    <audio id="audio_record" type="audio/wav" style="display: none;" controls></audio>
    <!-- 音量相关控件 -->
    <div class="volume-controls" style="display: none; margin-top: 15px;">
        <!-- 播放音量指示器 -->
        <!-- <div style="margin-bottom: 10px;">
            <div style="font-size:12px;color:#666;margin-bottom:5px;">播放音量</div>
            <div id="audio-volume-indicator" style="background:#f0f0f0;border-radius:4px;height:6px;width:100%;overflow:hidden;">
                <div id="audio-volume-bar" style="background:#2ec4b6;height:100%;width:0%;transition:width 0.1s;"></div>
            </div>
        </div> -->
        
        <!-- 音量阈值控制 - 改为开关 -->
        <div id="volume-threshold-control" style="margin-top: 15px; display: none;">
            <div class="switch-container" style="justify-content: space-between;">
                <label for="speaker-detection-toggle" style="margin: 0; font-size: 14px; color: var(--text-secondary);">扬声器检测 (自动暂停/恢复识别)</label>
                <label class="switch">
                    <input type="checkbox" id="speaker-detection-toggle" checked>
                    <span class="slider"></span>
                </label>
            </div>
            <!-- 隐藏的阈值显示，用于调试和保持兼容性 -->
            <span id="threshold-value-display" style="display:none;">1</span>
            <input type="range" id="threshold-slider" min="1" max="100" step="99" value="1" style="display:none;">
        </div>

        <!-- 清空历史记录选项 -->
        <div id="clear-history-control" style="margin-top: 15px; display: none;">
            <div class="switch-container" style="justify-content: space-between;">
                <label for="clear-history-toggle" style="margin: 0; font-size: 14px; color: var(--text-secondary);">每次进入页面时清空历史对话</label>
                <label class="switch">
                    <input type="checkbox" id="clear-history-toggle" checked>
                    <span class="slider"></span>
                </label>
            </div>
        </div>
    </div>
    <!-- 高级设置区域 -->
    <div class="divider" style="display: none;"></div>
    <div id="advanced-settings" class="hidden" style="display: none;">
        <!-- ASR模型选择 -->
        <div id="mic_mode_div" class="control-section">
            <h4>选择ASR模型模式</h4>
            <div class="radio-group">
                <div class="radio-option">
                    <input name="asr_mode" type="radio" value="2pass" id="2pass-radio" checked>
                    <label for="2pass-radio">2pass</label>
                </div>
                <div class="radio-option">
                    <input name="asr_mode" type="radio" value="online" id="online-radio">
                    <label for="online-radio">Online</label>
                </div>
                <div class="radio-option">
                    <input name="asr_mode" type="radio" value="offline" id="offline-radio">
                    <label for="offline-radio">Offline</label>
                </div>
            </div>
        </div>

        <!-- 文件上传 -->
        <div id="rec_mode_div" class="control-section hidden">
            <h4>选择音频文件</h4>
            <div class="file-upload">
                <label for="upfile" class="file-upload-label">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 8px; vertical-align: text-bottom;">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                        <polyline points="17 8 12 3 7 8"/>
                        <line x1="12" y1="3" x2="12" y2="15"/>
                    </svg>
                    选择音频文件
                </label>
                <input type="file" id="upfile" accept="audio/*">
            </div>
        </div>

        <!-- ITN选项 -->
        <div id="use_itn_div" class="control-section">
            <h4>反向文本归一化 (ITN)</h4>
            <div class="radio-group">
                <div class="radio-option">
                    <input name="use_itn" type="radio" value="false" id="itn-false" checked>
                    <label for="itn-false">否</label>
                </div>
                <div class="radio-option">
                    <input name="use_itn" type="radio" value="true" id="itn-true">
                    <label for="itn-true">是</label>
                </div>
            </div>
        </div>


        <!-- 热词 -->
        <div class="control-section hidden">
            <h4>热词设置</h4>
            <p style="margin: 0 0 8px 0; color: var(--text-secondary); font-size: 13px;">每行一个关键词，空格分隔权重，例如 "阿里巴巴 20"</p>
            <textarea id="varHot" rows="3">阿里巴巴 20
hello world 40</textarea>
        </div>
    </div>
    <!-- 显示/隐藏高级设置按钮 -->
    <button id="toggle-advanced" style="display: none; width: 100%; background-color: var(--background-dark); color: var(--text-primary);">
        显示高级设置
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-left: 8px; vertical-align: text-bottom;">
            <polyline points="6 9 12 15 18 9"></polyline>
        </svg>
    </button>
</div>

<script src="config.js" charset="UTF-8"></script>
<script src="recorder-core.js" charset="UTF-8"></script>
<script src="wav.js" charset="UTF-8"></script>
<script src="pcm.js" charset="UTF-8"></script>
<script src="wsconnecter.js" charset="utf-8"></script>
<script src="main.js" charset="utf-8"></script>
<!--<script src="../client.js"></script>-->
<script>
    // 高级设置切换
    document.getElementById('toggle-advanced').addEventListener('click', function() {
        const advancedSettings = document.getElementById('advanced-settings');
        const isHidden = advancedSettings.classList.contains('hidden');

        if (isHidden) {
            advancedSettings.classList.remove('hidden');
            this.innerHTML = `
                    隐藏高级设置
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-left: 8px; vertical-align: text-bottom;">
                        <polyline points="18 15 12 9 6 15"></polyline>
                    </svg>
                `;
        } else {
            advancedSettings.classList.add('hidden');
            this.innerHTML = `
                    显示高级设置
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-left: 8px; vertical-align: text-bottom;">
                        <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                `;
        }
    });

    // WebSocket相关变量
    window.socket = null;
    window.userId = "user_id"; // 固定用户ID
    window.selectedClientId = null;
    let autoSelectLatestClient = true; // 自动选择最新客户机
    
    // DOM元素引用
    // const statusElement = document.querySelector('.status');
    const loginSection = document.getElementById('login-section');
    const clientSelectionSection = document.getElementById('client-selection-section');
    const currentClientElement = document.getElementById('current-client');
    const currentClientStatus = document.getElementById('current-client-status');
    const messageContainer = document.getElementById('message-container');
    const connectBtn = document.getElementById('connect-btn');
    const disconnectBtn = document.getElementById('disconnect-btn');
    const refreshBtn = document.getElementById('refresh-btn');

    // 初始化按钮状态
    document.addEventListener('DOMContentLoaded', function() {
        // 从后端获取数字人列表
        fetchDigitalHumans();
        
        // 初始连接到服务器
        window.connectToServer(window.userId);
        
        // 刷新按钮点击事件
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                if (window.socket && window.socket.readyState === WebSocket.OPEN) {
                    window.socket.send(JSON.stringify({ refresh_clients: true }));
                    window.addMessage('系统', '正在刷新数字人列表...', 'system');
                    // 同时刷新数字人列表数据
                    fetchDigitalHumans();
                } else {
                    alert('未连接到服务器');
                    window.addMessage('系统', '未连接到服务器，无法刷新列表', 'system');
                }
            });
        }
        
        // 断开连接按钮点击事件
        if (disconnectBtn) {
            disconnectBtn.addEventListener('click', () => {
                if (window.socket && window.socket.readyState === WebSocket.OPEN) {
                    window.socket.close();
                }
            });
        }
        
        // 添加文本发送按钮事件处理
        const sendTextBtn = document.getElementById('send-text-btn');
        const textInput = document.getElementById('text-input');
        const messageType = document.getElementById('message-type');
        
        if (sendTextBtn && textInput) {
            sendTextBtn.addEventListener('click', function() {
                const text = textInput.value.trim();
                if (text) {
                    // 发送文本消息
                    window.sendTextMessage(text, messageType.value);
                    // 清空输入框
                    textInput.value = '';
                }
            });
            
            // 添加回车键发送功能
            textInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendTextBtn.click();
                }
            });
        }
        
        // 添加类型选择器变更事件
        if (messageType) {
            messageType.addEventListener('change', function() {
                const type = messageType.value;
                // 发送类型变更消息
                // window.sendTypeChange(type);
                window.messageType = type;
                console.log(`已切换到${type}模式`);
            });
        }

        // 添加开始对话按钮点击事件
        const startConversationBtn = document.getElementById('start-conversation-btn');
        if (startConversationBtn) {
            startConversationBtn.addEventListener('click', function() {
                startConversation();
            });
        }
    });

    // 从后端获取数字人列表
    function fetchDigitalHumans() {
        // 确保使用正确的协议
        
        fetch(`${window.host}/clients`,{
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            },
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('获取数字人列表失败: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                console.log('获取到数字人列表:', data);
                // 使用后端数据覆盖本地数字人列表
                window.digitalHumans = data;
                // 重新渲染数字人网格
                renderDigitalHumans();
            })
            .catch(error => {
                console.error('获取数字人列表出错:', error);
                // 加载失败时使用本地默认数据
                renderDigitalHumans();
            });
    }

    // 数字人预设数据（备用）
    window.digitalHumans = window.digitalHumans || [
        {
            id: "9291bb67-d6a8-466d-b9c2-1a5d7a2d6e3b",
            name: "智能助手小美",
            imageUrl: "./static/images/xiaomei.png",
            prompt: "你是一位亲切友好的AI助手，擅长回答各类问题，风格温和有礼。"
        },
        {
            id: "8ea1fc23-64c2-40bf-a76b-1cdd07b00add",
            name: "金融顾问李教授",
            imageUrl: "./static/images/xiaomei.png",
            prompt: "你是一位资深金融分析师，精通投资理财，能提供专业的金融建议。"
        },
        {
            id: "714fb18b-66e5-4a98-b8b8-93d54f57ad09",
            name: "旅游向导小王",
            imageUrl: "./static/images/xiaomei.png",
            prompt: "你是一位热情的旅游向导，熟悉世界各地的景点和文化，能为游客提供旅行建议。"
        },
        {
            id: "9562185f-6087-867b-dc99-f1g82bdcc58f",
            name: "厨艺大师陈师傅",
            imageUrl: "./static/images/xiaomei.png",
            prompt: "你是一位拥有三十年经验的中餐大厨，擅长各种菜系，能详细讲解烹饪技巧。"
        },
        {
            id: "1062185f-5087-967b-ec99-g1h82bdcc59f",
            name: "心理咨询师张医生",
            imageUrl: "./static/images/xiaomei.png",
            prompt: "你是一位专业的心理咨询师，擅长倾听和共情，能帮助人们解决心理困扰。"
        },
        {
            id: "1162185f-4087-167b-fc99-h1i82bdcc60f",
            name: "科技专家吴博士",
            imageUrl: "./static/images/xiaomei.png",
            prompt: "你是一位AI和计算机科学专家，了解最新科技动态，能解释复杂技术概念。"
        },
        {
            id: "1262185f-3087-267b-gc99-i1j82bdcc61f",
            name: "健身教练小林",
            imageUrl: "./static/images/xiaomei.png",
            prompt: "你是一位专业健身教练，熟悉各种运动和健康知识，能提供个性化健身计划。"
        },
        {
            id: "1362185f-2087-367b-hc99-j1k82bdcc62f",
            name: "音乐老师周老师",
            imageUrl: "./static/images/xiaomei.png",
            prompt: "你是一位有二十年教学经验的音乐老师，精通钢琴和声乐，能讲解音乐理论。"
        },
        {
            id: "1462185f-1087-467b-ic99-k1l82bdcc63f",
            name: "文学教授赵教授",
            imageUrl: "./static/images/xiaomei.png",
            prompt: "你是一位古典文学教授，熟悉中外文学作品，能进行深入的文学分析。"
        },
        {
            id: "1562185f-0087-567b-jc99-l1m82bdcc64f",
            name: "儿童教育专家黄老师",
            imageUrl: "./static/images/xiaomei.png",
            prompt: "你是一位儿童教育专家，了解儿童心理和发展规律，能提供育儿和教育建议。"
        }
    ];

    // 全局变量，用于存储当前选择的数字人信息
    window.selectedDigitalHuman = null;
    window.selectedClientId = null;

    // 连接到服务器
    window.connectToServer = function(userId) {
        // 使用传入的userId参数，而不是全局变量
        const wsUrl = `${window.wsProtocol}://${window.ochost}/ws/user/${userId}`;
        console.log('正在连接WebSocket:', wsUrl);

        // 关闭现有连接
        if (window.socket && window.socket.readyState === WebSocket.OPEN) {
            window.socket.close();
        }
        
        window.socket = new WebSocket(wsUrl);
        
        window.socket.onopen = () => {
            console.log('已连接到服务器');
            // if (statusElement) {
            //     statusElement.textContent = '状态: 已连接';
            //     statusElement.className = 'status status-connected';
            // }
            if (connectBtn) connectBtn.disabled = true;
            if (disconnectBtn) disconnectBtn.disabled = false;
            if (clientSelectionSection) clientSelectionSection.classList.remove('hidden');
            
            // const infoDiv = document.getElementById('info_div');
            // if (infoDiv) infoDiv.textContent = '已连接服务器，等待接收端连接';
            
            if (currentClientStatus) currentClientStatus.textContent = '已连接服务器，等待接收端';
            
            // 添加连接成功消息
            window.addMessage('系统', '已连接到服务器', 'system');
            
            // 连接后立即刷新客户机列表并加载预设数字人
            window.socket.send(JSON.stringify({ refresh_clients: true }));
            renderDigitalHumans();
        };
        
        window.socket.onmessage = (event) => {
            const data = JSON.parse(event.data);
            console.log('收到WebSocket消息:', data);
            
            if (data.type === 'client_list') {
                // 在更新客户机列表前尝试获取最新的数字人数据
                setTimeout(() => {
                updateClientList(data.clients);
                // 显示客户端列表消息
                if (data.clients && data.clients.length > 0) {
                        window.addMessage('系统', `发现${data.clients.length}个接收端，请选择一个数字人`, 'system');
                } else {
                        window.addMessage('系统', '没有找到可用的数字人接收端', 'system');
                }
                }, 300); // 给fetchDigitalHumans一点时间完成
                
                fetchDigitalHumans();
                // 不再自动选择最新的客户机，让用户手动选择数字人形象
                /*
                if (autoSelectLatestClient && data.clients && data.clients.length > 0) {
                    const latestClient = data.clients[data.clients.length - 1];
                    if (latestClient !== window.selectedClientId) {
                        selectClient(latestClient);
                    }
                }
                */
            } else if (data.type === 'client_selected') {
                if (data.success) {
                    window.selectedClientId = data.client_id;
                    
                    // 找到对应的数字人
                    const selectedHuman = window.digitalHumans.find(human => human.id === data.client_id);
                    if (selectedHuman) {
                        // 存储选中的数字人信息到全局变量
                        window.selectedDigitalHuman = selectedHuman;
                        
                        // 更新UI显示
                        const currentClientName = document.getElementById('current-client-name');
                        if (currentClientName) {
                            currentClientName.textContent = selectedHuman.name;
                        }
                        document.getElementById('current-client').textContent = data.client_id;
                        document.getElementById('current-client-id-display').style.display = 'block';
                        
                        // 显示开始对话按钮
                        document.getElementById('start-conversation-btn').style.display = 'block';
                        document.getElementById('info_div').style.display = 'block';
                        document.getElementById('info_div').innerHTML = `<span style="color:#2ec4b6">✓ 已选择 ${selectedHuman.name}，点击"开始对话"按钮进入交互界面</span>`;
                        
                        console.log(`已选择数字人: ${selectedHuman.name} (ID: ${data.client_id})`);
                        if (currentClientStatus) currentClientStatus.textContent = `已连接到数字人: ${selectedHuman.name}`;
                    
                    // 添加选择成功消息
                        window.addMessage('系统', `已选择数字人: ${selectedHuman.name}`, 'system');
                } else {
                        // 如果找不到对应的预设数字人
                        if (currentClientStatus) currentClientStatus.textContent = `已连接到接收端: ${data.client_id}`;
                        window.addMessage('系统', `已连接到接收端: ${data.client_id}`, 'system');
                    }
                } else {
                    console.error('选择数字人失败');
                    if (currentClientStatus) currentClientStatus.textContent = '选择数字人失败';
                    
                    // 添加选择失败消息
                    window.addMessage('系统', '选择数字人失败', 'system');
                }
            } else if (data.type === 'message_sent') {
                console.log(`消息已发送到接收端: ${data.client_id}`);
                // 添加消息发送成功信息
                window.addMessage('系统', `消息已发送到接收端: ${data.client_id}`, 'system');
            } else if (data.type === 'type_change_sent') {
                console.log(`类型变更消息已发送到接收端: ${data.client_id}`);
                // 添加类型变更成功消息
                window.addMessage('系统', `类型变更消息已发送到接收端: ${data.client_id}`, 'system');
            } else if (data.type === 'client_disconnected') {
                console.log(`接收端已断开连接: ${data.client_id}`);
                window.addMessage('系统', `接收端已断开连接: ${data.client_id}`, 'system');
                
                if (window.selectedClientId === data.client_id) {
                    window.selectedClientId = null;
                    window.selectedDigitalHuman = null;
                    
                    // 重置UI
                    const currentClientName = document.getElementById('current-client-name');
                    if (currentClientName) {
                        currentClientName.textContent = '无';
                    }
                    document.getElementById('current-client').textContent = '无';
                    document.getElementById('current-client-id-display').style.display = 'none';
                    
                    // 隐藏开始对话按钮
                    document.getElementById('start-conversation-btn').style.display = 'none';
                    document.getElementById('info_div').style.display = 'none';
                    
                    if (currentClientStatus) currentClientStatus.textContent = '数字人已断开，等待新连接';
                    
                    // 移除选中样式
                    const cards = document.querySelectorAll('.digital-human-card');
                    cards.forEach(card => {
                        card.classList.remove('selected');
                    });
                }
                // 刷新客户机列表
                window.socket.send(JSON.stringify({ refresh_clients: true }));
            } else if (data.type === 'error') {
                console.error(`WebSocket错误: ${data.message}`);
                if (currentClientStatus) currentClientStatus.textContent = `错误: ${data.message}`;
                //
                // const infoDiv = document.getElementById('info_div');
                // if (infoDiv) infoDiv.textContent = `错误: ${data.message}`;
                
                // 添加错误消息
                window.addMessage('错误', data.message, 'system');
            }
        };
        
        window.socket.onclose = () => {
            console.log('与服务器的连接已关闭');
            // if (statusElement) {
            //     statusElement.textContent = '状态: 未连接';
            //     statusElement.className = 'status status-disconnected';
            // }
            if (connectBtn) connectBtn.disabled = false;
            if (disconnectBtn) disconnectBtn.disabled = true;
            if (clientSelectionSection) clientSelectionSection.classList.remove('hidden');
            
            window.selectedClientId = null;
            
            // const infoDiv = document.getElementById('info_div');
            // if (infoDiv) infoDiv.textContent = '与服务器断开连接，尝试重连中...';
            
            if (currentClientStatus) currentClientStatus.textContent = '未连接到服务器';
            
            // 注释掉不存在的元素引用
            /*
            document.getElementById('btnStart').disabled = true;
            document.getElementById('btnStop').disabled = true;
            */
            
            // 添加断开连接消息
            window.addMessage('系统', '与服务器的连接已断开', 'system');
            
            // 自动重连
            setTimeout(() => {
                window.connectToServer(window.userId);
            }, 5000);
        };
        
        window.socket.onerror = (error) => {
            console.error('WebSocket错误:', error);
            if (currentClientStatus) currentClientStatus.textContent = '连接错误，请检查网络';
            //
            // const infoDiv = document.getElementById('info_div');
            // if (infoDiv) infoDiv.textContent = '连接错误，请检查网络';
            
            // 添加错误消息
            window.addMessage('系统', '连接错误，请检查网络', 'system');
        };
    }
    
    // 更新客户机列表
    function updateClientList(clients) {
        // 找出预设中与服务器返回的ID匹配的数字人
        const availableHumans = window.digitalHumans.filter(human => clients.includes(human.id));
        
        // 如果没有匹配的预设数字人，显示空状态
        if (availableHumans.length === 0) {
            const digitalHumanGrid = document.getElementById('digital-human-grid');
            if (digitalHumanGrid) {
                digitalHumanGrid.innerHTML = '<div class="loading-indicator">没有可用的数字人接收端</div>';
            }
            
            if (currentClientStatus) {
                currentClientStatus.textContent = '没有可用的数字人接收端';
            }
            
            window.addMessage('系统', '没有找到可用的数字人接收端', 'system');
            
            // 没有可用的数字人时，刷新数字人列表数据
            fetchDigitalHumans();
            return;
        }
        
        // 渲染可用的数字人
        const digitalHumanGrid = document.getElementById('digital-human-grid');
        if (!digitalHumanGrid) return;
        
        // 清空网格
        digitalHumanGrid.innerHTML = '';
        
        // 为每个可用的数字人创建卡片
        availableHumans.forEach(human => {
            const card = createDigitalHumanCard(human);
            digitalHumanGrid.appendChild(card);
        });
        
        // 自动选择第一个数字人
        if (autoSelectLatestClient && availableHumans.length > 0 && !window.selectedClientId) {
            selectDigitalHuman(availableHumans[0]);
        }
    }
    
    // 选择数字人
    function selectDigitalHuman(human) {
        if (window.socket && window.socket.readyState === WebSocket.OPEN) {
            console.log(`选择数字人: ${human.name} (ID: ${human.id})`);
            
            // 更新UI显示选中状态
            const cards = document.querySelectorAll('.digital-human-card');
            cards.forEach(card => {
                if (card.dataset.id === human.id) {
                    card.classList.add('selected');
                } else {
                    card.classList.remove('selected');
                }
            });
            
            // 存储选中的数字人信息到全局变量
            window.selectedDigitalHuman = human;
            
            // 更新当前选择的数字人信息
            const currentClientName = document.getElementById('current-client-name');
            if (currentClientName) {
                currentClientName.textContent = human.name;
            }
            
            // 显示ID（可选）
            document.getElementById('current-client').textContent = human.id;
            document.getElementById('current-client-id-display').style.display = 'block';
            
            // 发送选择消息到服务器
            window.socket.send(JSON.stringify({ select_client: human.id }));
            window.addMessage('系统', `已选择数字人: ${human.name}`, 'system');
            
            // 显示开始对话按钮
            document.getElementById('start-conversation-btn').style.display = 'block';
            document.getElementById('info_div').style.display = 'block';
            document.getElementById('info_div').innerHTML = `<span style="color:#2ec4b6">✓ 已选择 ${human.name}，点击"开始对话"按钮进入交互界面</span>`;
        } else {
            console.error('无法选择数字人：WebSocket未连接');
            window.addMessage('系统', '未连接到服务器，无法选择数字人', 'system');
        }
    }
    
    // 渲染数字人网格
    function renderDigitalHumans() {
        const digitalHumanGrid = document.getElementById('digital-human-grid');
        if (!digitalHumanGrid) return;
        
        // 清空网格
        digitalHumanGrid.innerHTML = '';
        
        if (window.digitalHumans.length === 0) {
            digitalHumanGrid.innerHTML = '<div class="loading-indicator">没有可用的数字人</div>';
            return;
        }
        
        // 为每个数字人创建卡片
        window.digitalHumans.forEach(human => {
            const card = createDigitalHumanCard(human);
            digitalHumanGrid.appendChild(card);
        });
    }
    
    // 创建数字人卡片
    function createDigitalHumanCard(human) {
        const card = document.createElement('div');
        card.className = 'digital-human-card';
        card.dataset.id = human.id;
        
        // 如果是当前选中的数字人，添加选中样式
        if (human.id === window.selectedClientId) {
            card.classList.add('selected');
        }
        
        const imageContainer = document.createElement('div');
        imageContainer.className = 'digital-human-image-container';
        
        const image = document.createElement('img');
        image.className = 'digital-human-image';
        image.src = human.imageUrl;
        image.alt = human.name;
        image.loading = 'lazy'; // 延迟加载图片
        
        const info = document.createElement('div');
        info.className = 'digital-human-info';
        
        const name = document.createElement('div');
        name.className = 'digital-human-name';
        name.textContent = human.name;
        
        const description = document.createElement('div');
        description.className = 'digital-human-description';
        description.textContent = human.prompt;
        
        info.appendChild(name);
        info.appendChild(description);
        
        imageContainer.appendChild(image);
        
        card.appendChild(imageContainer);
        card.appendChild(info);
        
        // 添加点击事件处理
        card.addEventListener('click', function() {
            selectDigitalHuman(human);
        });
        
        return card;
    }

    // 添加消息到消息容器（简化版，只记录日志）
    window.addMessage = function(sender, text, type) {
        // 创建现在的时间
        const now = new Date();
        const timeStr = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
        
        // 只输出到控制台
        console.log(`${timeStr} - ${sender}: ${text} (${type})`);
    }

    // 跳转到对话界面
    function startConversation() {
        if (!window.selectedDigitalHuman) {
            alert('请先选择一个数字人');
            return;
        }
        
        // 将选中的数字人信息存储到sessionStorage
        sessionStorage.setItem('selectedDigitalHuman', JSON.stringify(window.selectedDigitalHuman));
        sessionStorage.setItem('selectedClientId', window.selectedClientId);
        
        // 跳转到对话界面
        window.location.href = 'oc-panel.html';
    }
</script>
</body>
</html>