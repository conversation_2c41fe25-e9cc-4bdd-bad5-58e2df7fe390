<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>唤醒词存储测试</title>
</head>
<body>
    <h1>唤醒词存储测试</h1>
    <div>
        <h2>测试步骤：</h2>
        <ol>
            <li>打开浏览器开发者工具的控制台</li>
            <li>点击下面的按钮进行测试</li>
            <li>查看控制台输出结果</li>
        </ol>
    </div>
    
    <div>
        <button onclick="testWakeWordsStorage()">测试唤醒词存储功能</button>
        <button onclick="clearWakeWordsStorage()">清除存储的唤醒词</button>
    </div>
    
    <div id="result"></div>

    <script type="module">
        // 导入存储工具
        import { setStorage, getStorage, removeStorage } from './utils/storage.js';
        
        // 存储命名空间配置
        const STORAGE_NAMESPACE = {
            WAKE_WORDS: 'digitalHuman:wakeWords',
        };
        
        // 测试唤醒词存储功能
        window.testWakeWordsStorage = function() {
            const resultDiv = document.getElementById('result');
            let output = '<h3>测试结果：</h3>';
            
            try {
                // 1. 测试设置唤醒词到存储
                const testWakeWords = {
                    "小幸运": "你好",
                    "小乐": "你好",
                    "小助手": "在的",
                    "小美": "有什么可以帮您的"
                };
                
                console.log('1. 设置测试唤醒词到存储...');
                const setResult = setStorage(STORAGE_NAMESPACE.WAKE_WORDS, testWakeWords);
                output += `<p>设置唤醒词到存储: ${setResult ? '成功' : '失败'}</p>`;
                console.log('设置结果:', setResult);
                
                // 2. 测试从存储获取唤醒词
                console.log('2. 从存储获取唤醒词...');
                const retrievedWakeWords = getStorage(STORAGE_NAMESPACE.WAKE_WORDS);
                output += `<p>从存储获取唤醒词: ${retrievedWakeWords ? '成功' : '失败'}</p>`;
                console.log('获取的唤醒词:', retrievedWakeWords);
                
                // 3. 验证数据一致性
                const isConsistent = JSON.stringify(testWakeWords) === JSON.stringify(retrievedWakeWords);
                output += `<p>数据一致性检查: ${isConsistent ? '通过' : '失败'}</p>`;
                console.log('数据一致性:', isConsistent);
                
                // 4. 显示获取到的唤醒词
                if (retrievedWakeWords) {
                    output += '<p>存储的唤醒词:</p><ul>';
                    for (const [word, value] of Object.entries(retrievedWakeWords)) {
                        output += `<li>${word} -> ${value}</li>`;
                    }
                    output += '</ul>';
                }
                
                output += '<p style="color: green;">✅ 唤醒词存储功能测试完成！</p>';
                
            } catch (error) {
                output += `<p style="color: red;">❌ 测试过程中出现错误: ${error.message}</p>`;
                console.error('测试错误:', error);
            }
            
            resultDiv.innerHTML = output;
        };
        
        // 清除存储的唤醒词
        window.clearWakeWordsStorage = function() {
            const resultDiv = document.getElementById('result');
            
            try {
                console.log('清除存储的唤醒词...');
                const removeResult = removeStorage(STORAGE_NAMESPACE.WAKE_WORDS);
                
                const output = `
                    <h3>清除结果：</h3>
                    <p>清除存储的唤醒词: ${removeResult ? '成功' : '失败'}</p>
                    <p style="color: orange;">🧹 存储已清除！</p>
                `;
                
                resultDiv.innerHTML = output;
                console.log('清除结果:', removeResult);
                
            } catch (error) {
                const output = `
                    <h3>清除结果：</h3>
                    <p style="color: red;">❌ 清除过程中出现错误: ${error.message}</p>
                `;
                resultDiv.innerHTML = output;
                console.error('清除错误:', error);
            }
        };
        
        // 页面加载时检查当前存储状态
        window.addEventListener('load', function() {
            console.log('检查当前存储状态...');
            const currentWakeWords = getStorage(STORAGE_NAMESPACE.WAKE_WORDS);
            console.log('当前存储的唤醒词:', currentWakeWords);
        });
    </script>
</body>
</html>
