# 语音识别网站 (appszr.ai-tulip.site)

这是从 https://appszr.ai-tulip.site/ 下载的完整网站代码。

## 文件结构

```
appszr-website/
├── index.html          # 主页面 - 语音识别界面
├── oc-panel.html       # 数字人语音交互页面
├── favicon.ico         # 网站图标
├── config.js           # 配置文件
├── main.js             # 主要JavaScript逻辑
├── ws-bridge.js        # WebSocket桥接
├── wsconnecter.js      # WebSocket连接器
├── recorder-core.js    # 录音核心功能
├── wav.js              # WAV音频处理
├── pcm.js              # PCM音频处理
└── README.md           # 本说明文件
```

## 功能说明

### 主要功能
- 语音识别 (ASR)
- 数字人远程语音控制
- 实时音频录制和处理
- WebSocket通信
- 多种音频格式支持 (WAV, PCM)

### 页面说明
1. **index.html** - 主页面，包含：
   - 数字人选择界面
   - 语音识别设置
   - 音频文件上传
   - 实时语音识别

2. **oc-panel.html** - 对话面板，包含：
   - 数字人语音交互界面
   - 消息记录
   - 高级设置选项

## 技术特性

- 支持多种ASR模型模式 (2pass, Online, Offline)
- 反向文本归一化 (ITN)
- 热词设置功能
- 扬声器检测和自动暂停/恢复
- 响应式设计，支持移动端

## 使用方法

1. 在本地Web服务器中部署这些文件
2. 访问 index.html 开始使用
3. 配置WebSocket连接地址
4. 选择数字人形象
5. 开始语音识别和交互

## 依赖说明

- 需要WebSocket服务器支持
- 需要现代浏览器支持Web Audio API
- 需要HTTPS环境以使用麦克风功能

## 下载信息

- 下载时间: 2025年7月29日
- 原始网站: https://appszr.ai-tulip.site/
- 下载的文件包含了网站的完整前端代码
