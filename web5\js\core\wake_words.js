import { getPinyinString, getPinyinWithTone, isPinyinMatch } from '../../utils/pinyin.js';
import { setStorage, getStorage } from '../../utils/storage.js';

/**
 * 唤醒词模块 - 管理数字人唤醒词配置和匹配
 */

// 存储命名空间配置
const STORAGE_NAMESPACE = {
    WAKE_WORDS: 'digitalHuman:wakeWords',
};

// 默认唤醒词配置
const DEFAULT_WAKE_WORDS = {
    "小幸运": "你好",
    "小乐": "你好",
};

// 定义唤醒词map映射
let wakeWords = {
    ...DEFAULT_WAKE_WORDS
};

/**
 * 根据唤醒词获取对应的数字人名称
 * @param {string} word - 唤醒词
 * @returns {string} 数字人名称或原词
 */
export function getWakeWord(word) {
    return wakeWords[word] || word;
}

/**
 * 获取唤醒词列表
 * @returns {string[]} 唤醒词数组
 */
export function getWakeWords() {
    return Object.keys(wakeWords);
}

/**
 * 获取唤醒词及其对应的数字人名称
 * @returns {Object} 唤醒词映射对象
 */
export function getWakeWordsMap() {
    return {...wakeWords};
}

/**
 * 设置唤醒词
 * @param {string} word - 唤醒词
 * @param {string} value - 对应的数字人名称
 */
export function setWakeWord(word, value) {
    wakeWords[word] = value;
    saveWakeWordsToStorage();
}

/**
 * 删除唤醒词
 * @param {string} word - 要删除的唤醒词
 * @returns {boolean} 是否删除成功
 */
export function deleteWakeWord(word) {
    if (wakeWords.hasOwnProperty(word)) {
        delete wakeWords[word];
        saveWakeWordsToStorage();
        return true;
    }
    return false;
}

/**
 * 新增唤醒词
 * @param {string} word - 唤醒词
 * @param {string} value - 对应的数字人名称
 */
export function addWakeWord(word, value) {
    wakeWords[word] = value;
    saveWakeWordsToStorage();
    return true;
}

/**
 * 批量设置唤醒词
 * @param {Object} wordsMap - 唤醒词映射对象
 */
export function setWakeWords(wordsMap) {
    if (wordsMap && typeof wordsMap === 'object') {
        wakeWords = {...wordsMap};
        saveWakeWordsToStorage();
    }
}

/**
 * 重置唤醒词为默认配置
 */
export function resetWakeWords() {
    wakeWords = {...DEFAULT_WAKE_WORDS};
    saveWakeWordsToStorage();
}

/**
 * 获取唤醒词的无声调拼音
 * @param {string} word - 唤醒词
 * @returns {string} 无声调拼音
 */
export function getWakeWordPinyinString(word) {
    return getPinyinString(word);
}

/**
 * 获取唤醒词的带声调拼音
 * @param {string} word - 唤醒词
 * @returns {string} 带声调拼音
 */
export function getWakeWordPinyinWithTone(word) {
    return getPinyinWithTone(word);
}

/**
 * 通过拼音匹配检查文本是否包含唤醒词
 * @param {string} text - 要检查的文本
 * @returns {Object|null} 匹配到的唤醒词信息，如果没匹配则返回null
 */
export function matchWakeWordByPinyin(text) {
    if (!text) return null;
    
    const textPinyin = getPinyinString(text);
    
    for (const word of Object.keys(wakeWords)) {
        const wordPinyin = getPinyinString(word);
        if (textPinyin.includes(wordPinyin)) {
            return {
                word: word,
                value: wakeWords[word],
                matchedText: text
            };
        }
    }
    
    return null;
}

/**
 * 精确匹配文本中是否包含唤醒词
 * @param {string} text - 要检查的文本
 * @returns {Object|null} 匹配到的唤醒词信息，如果没匹配则返回null
 */
export function matchWakeWordExact(text) {
    if (!text) return null;
    
    for (const word of Object.keys(wakeWords)) {
        if (text.includes(word)) {
            return {
                word: word,
                value: wakeWords[word],
                matchedText: text
            };
        }
    }
    
    return null;
}

/**
 * 将唤醒词配置保存到存储
 * @private
 */
function saveWakeWordsToStorage() {
    try {
        setStorage(STORAGE_NAMESPACE.WAKE_WORDS, wakeWords);
    } catch (err) {
        console.error('保存唤醒词配置失败:', err);
    }
}

/**
 * 从存储加载唤醒词配置
 * @returns {boolean} 是否成功加载配置
 */
export function loadWakeWordsFromStorage() {
    try {
        const savedConfig = getStorage(STORAGE_NAMESPACE.WAKE_WORDS);
        if (savedConfig && typeof savedConfig === 'object') {
            wakeWords = savedConfig;
            return true;
        }
    } catch (err) {
        console.error('加载唤醒词配置失败:', err);
    }
    return false;
}

// 初始化时尝试从localStorage加载配置
loadWakeWordsFromStorage();

window.getWakeWordsMap = getWakeWordsMap;




